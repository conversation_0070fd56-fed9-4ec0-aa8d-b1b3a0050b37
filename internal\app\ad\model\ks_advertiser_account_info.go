// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2025-08-13 15:02:27
// 生成路径: internal/app/ad/model/ks_advertiser_account_info.go
// 生成人：cyao
// desc:快手广告账户表格
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
	ksApi "github.com/tiger1103/gfast/v3/library/advertiser/ks/api"
)

// KsAdvertiserAccountInfoInfoRes is the golang structure for table ks_advertiser_account_info.
type KsAdvertiserAccountInfoInfoRes struct {
	gmeta.Meta            `orm:"table:ks_advertiser_account_info"`
	AccountId             int64       `orm:"account_id,primary" json:"accountId" dc:"广告主ID"`                                          // 广告主ID
	AgentAccountId        int64       `orm:"agent_account_id" json:"agentAccountId" dc:"代理商账户id"`                                   // 代理商账户id
	AuthorizeKsAccount    int64       `orm:"authorize_ks_account" json:"authorizeKsAccount" dc:"授权快手账号(代理商)"`                   // 授权快手账号(代理商)
	UserId                int64       `orm:"user_id" json:"userId" dc:"快手账户ID"`                                                      // 快手账户ID
	AccountName           string      `orm:"account_name" json:"accountName" dc:"快手账户名称"`                                          // 快手账户名称
	ResponsiblePerson     string      `orm:"responsible_person" json:"responsiblePerson" dc:"销售责任人"`                                // 销售责任人
	UcType                string      `orm:"uc_type" json:"ucType" dc:"账户类型"`                                                        // 账户类型
	PaymentType           string      `orm:"payment_type" json:"paymentType" dc:"付款类型"`                                              // 付款类型
	Balance               float64     `orm:"balance" json:"balance" dc:"现金余额"`                                                       // 现金余额
	CreditBalance         float64     `orm:"credit_balance" json:"creditBalance" dc:"信用账户余额"`                                      // 信用账户余额
	ExtendedBalance       float64     `orm:"extended_balance" json:"extendedBalance" dc:"预留账户余额"`                                  // 预留账户余额
	Rebate                int64       `orm:"rebate" json:"rebate" dc:"后返余额"`                                                         // 后返余额
	PreRebate             int64       `orm:"pre_rebate" json:"preRebate" dc:"前返余额"`                                                  // 前返余额
	ContractRebate        int64       `orm:"contract_rebate" json:"contractRebate" dc:"框返余额"`                                        // 框返余额
	TotalBalance          int64       `orm:"total_balance" json:"totalBalance" dc:"总余额"`                                              // 总余额
	LoLimit               int64       `orm:"lo_limit" json:"loLimit" dc:"账户最低余额"`                                                  // 账户最低余额
	SingleOut             int64       `orm:"single_out" json:"singleOut" dc:"单次转账金额"`                                              // 单次转账金额
	AutoOut               int         `orm:"auto_out" json:"autoOut" dc:"自动转账状态"`                                                  // 自动转账状态
	BalanceWarn           int         `orm:"balance_warn" json:"balanceWarn" dc:"余额不足提醒"`                                          // 余额不足提醒
	ProductName           string      `orm:"product_name" json:"productName" dc:"产品名称"`                                              // 产品名称
	FirstCostDay          string      `orm:"first_cost_day" json:"firstCostDay" dc:"首日消耗日期"`                                       // 首日消耗日期
	Industry              string      `orm:"industry" json:"industry" dc:"一级行业"`                                                     // 一级行业
	SecondIndustry        string      `orm:"second_industry" json:"secondIndustry" dc:"二级行业"`                                        // 二级行业
	Recharged             int         `orm:"recharged" json:"recharged" dc:"是否充值"`                                                   // 是否充值
	CorporationName       string      `orm:"corporation_name" json:"corporationName" dc:"企业名称"`                                      // 企业名称
	ReviewStatus          int         `orm:"review_status" json:"reviewStatus" dc:"审核状态 1-审核中; 2-审核通过; 3-审核拒绝; 0-待提交"` // 审核状态 1-审核中; 2-审核通过; 3-审核拒绝; 0-待提交
	FrozenStatus          int         `orm:"frozen_status" json:"frozenStatus" dc:"冻结状态"`                                            // 冻结状态
	TransferAccountStatus int         `orm:"transfer_account_status" json:"transferAccountStatus" dc:"转账状态"`                         // 转账状态
	ChildReviewStatusInfo string      `orm:"child_review_status_info" json:"childReviewStatusInfo" dc:"审核状态信息（嵌套结构体）"`        // 审核状态信息（嵌套结构体）
	CopyAccount           int         `orm:"copy_account" json:"copyAccount" dc:"是否为复制账户"`                                        // 是否为复制账户
	ReviewDetail          string      `orm:"review_detail" json:"reviewDetail" dc:"审核详情（数组嵌套结构体）"`                            // 审核详情（数组嵌套结构体）
	DirectRebate          int64       `orm:"direct_rebate" json:"directRebate" dc:"激励余额"`                                            // 激励余额
	OptimizerOwner        string      `orm:"optimizer_owner" json:"optimizerOwner" dc:"优化师责任人"`                                    // 优化师责任人
	AccountAutoManage     int         `orm:"account_auto_manage" json:"accountAutoManage" dc:"账户智投开关"`                             // 账户智投开关
	DayBudget             int64       `orm:"day_budget" json:"dayBudget" dc:"单日预算 单位：厘"`                                          // 单日预算 单位：厘
	Remark                string      `orm:"remark" json:"remark" dc:"备注"`                                                             // 备注
	AuthSource            string      `orm:"auth_source" json:"authSource" dc:"授权来源（如代理商账户授权 1 广告主授权 2）"`               // 授权来源（如代理商账户授权 1 广告主授权 2）
	AuthStatus            int         `orm:"auth_status" json:"authStatus" dc:"授权状态（0:待授权, 1:已授权）"`                            // 授权状态（0:待授权, 1:已授权）
	DeliveryStatus        int         `orm:"delivery_status" json:"deliveryStatus" dc:"投放状态（0:停用, 1:启用）"`                        // 投放状态（0:停用, 1:启用）
	Owner                 int         `orm:"owner" json:"owner" dc:"归属人员"`                                                           // 归属人员
	DeliveryType          int         `orm:"delivery_type" json:"deliveryType" dc:"投放方式（0:默认, 1:优先效果）暂时先没用到"`            // 投放方式（0:默认, 1:优先效果）暂时先没用到
	EffectFirst           int         `orm:"effect_first" json:"effectFirst" dc:"优先效果策略（1:开启, 其他:未开启）暂时先没用到"`         // 优先效果策略（1:开启, 其他:未开启）暂时先没用到
	CreateTime            int64       `orm:"create_time" json:"createTime" dc:"创建时间"`                                                // 创建时间
	CreatedAt             *gtime.Time `orm:"created_at" json:"createdAt" dc:"创建时间记录产生时间"`                                      // 创建时间记录产生时间
	UpdatedAt             *gtime.Time `orm:"updated_at" json:"updatedAt" dc:"更新时间"`                                                  // 更新时间
}

type KsAdvertiserAccountInfoListRes struct {
	AccountId          int64   `json:"accountId" dc:"账户ID"`
	AccountName        string  `json:"accountName" dc:"快手账户名称"`
	UserId             int64   `json:"userId" dc:"快手ID"`
	Remark             string  `json:"remark" dc:"备注"`
	AuthSource         string  `json:"authSource" dc:"授权来源（如代理商账户授权 1 广告主授权 2）"`
	AgentAccountId     int64   `json:"agentAccountId" dc:"代理商账户id"`
	AuthorizeKsAccount int64   `json:"authorizeKsAccount" dc:"授权快手账号(代理商)"`
	AuthStatus         int     `json:"authStatus" dc:"授权状态（0:待授权, 1:已授权）"`
	DeliveryStatus     int     `json:"deliveryStatus" dc:"投放状态（0:停用, 1:启用）"`
	Owner              int     `json:"owner" dc:"归属人员"`
	OwnerUserName      string  `json:"ownerUserName" dc:"归属人员名称"`
	Balance            float64 `json:"balance" dc:"现金余额"`
	TotalBalance       int64   `json:"totalBalance" dc:"总余额"`
	DayBudget          int64   `json:"dayBudget" dc:"单日预算 单位：厘"`
	CreateTime         int64   `json:"createTime" dc:"创建时间"`
}

// KsAdvertiserAccountInfoSearchReq 分页请求参数
type KsAdvertiserAccountInfoSearchReq struct {
	comModel.PageReq
	AuthSource      string   `p:"authSource" dc:"授权来源（如代理商账户授权 1 广告主授权 2）"`                                                //授权来源（如代理商账户授权 1 广告主授权 2）
	AuthStatus      string   `p:"authStatus" v:"authStatus@integer#授权状态（0:待授权, 1:已授权）需为整数" dc:"授权状态（0:待授权, 1:已授权）"` //授权状态（0:待授权, 1:已授权）
	DeliveryStatus  string   `p:"deliveryStatus" v:"deliveryStatus@integer#投放状态（0:停用, 1:启用）需为整数" dc:"投放状态（0:停用, 1:启用）"` //投放状态（0:停用, 1:启用）
	BalanceStatus   int      `p:"balanceStatus"  dc:"余额情况 0 不参与条件 1 余额>0 2 余额 = 0"`                                            // 余额情况
	Owner           string   `p:"owner" v:"owner@integer#归属人员需为整数" dc:"归属人员"`                                                   //归属人员
	CorporationName string   `p:"corporationName" dc:"企业名称"`
	AccountId       int64    `p:"accountId" dc:"账户ID"`
	KeyWord         string   `p:"keyword" dc:"快手账户名称"` //快手账户名称
	AccountIds      []int64  `p:"accountIds" dc:"账户IDs"`
	AccountNames    []string `p:"accountNames" dc:"快手账户名称"` //快手账户名称

	StartTime string `p:"startTime" v:"startTime@datetime#开始时间需为YYYY-MM-DD hh:mm:ss格式" dc:"开始时间"`
	EndTime   string `p:"endTime" v:"endTime@datetime#结束时间需为YYYY-MM-DD hh:mm:ss格式" dc:"结束时间"`
}

type KsAdvertiserAccountInfoImportReq struct {
	AgentId            int64 `p:"agentId" v:"required#代理商ID不能为空" dc:"代理商ID"`
	AuthorizeKsAccount int64 `p:"authorizeKsAccount" v:"required#授权账户不能为空" dc:"授权账户"`
	Owner              int64 `p:"owner" v:"required#归属人员不能为空" dc:"归属人员"`
	AccountIds         []int `p:"accountIds" v:"required#账户ID不能为空" dc:"广告账户IDs"`
}

type KsAdvertiserGetImportListReq struct {
	comModel.PageReq
	AgentId            int64  `p:"agentId" v:"required#代理商ID不能为空" dc:"代理商ID"`
	AuthorizeKsAccount int64  `p:"authorizeKsAccount" v:"required#授权账户不能为空" dc:"授权账户"`
	CorporationName    string `p:"corporationName" dc:"企业名称"`
	AccountId          string `p:"accountId" dc:"广告主ID"`
	AccountName        string `p:"accountName" dc:"账户名"`
	IsNoAuthor         bool   `p:"isNoAuthor" dc:"只查看无授权账户"`
}

type KsAdvertiserGetImportListRes struct {
	comModel.ListRes
	List []*KsAdvertiserGetImportRes `json:"list"`
}

type KsAdvertiserGetImportRes struct {
	AccountId          int64  `json:"accountId" dc:"广告主ID"`
	AccountName        string `json:"accountName" dc:"快手账户名称"`
	AgentAccountId     int64  `json:"agentAccountId" dc:"代理商账户id"`
	AuthorizeKsAccount int64  `json:"authorizeKsAccount" dc:"授权快手账号(代理商)"`
	CorporationName    string `json:"corporationName"  dc:"企业名称"`
	Owner              int64  `json:"owner" dc:"归属人员id"`
	OwnerUserName      string `json:"ownerUserName" dc:"归属人员名称"`
}

type KsAdvertiserAccountInfoSetOwnerReq struct {
	AccountIds []int64 `p:"accountIds" v:"required#账户ID不能为空" dc:"账户IDs"`
	Owner      int     `p:"owner" v:"required#归属人员不能为空" dc:"归属人员id"`
}

// KsAdvertiserAccountInfoSearchRes 列表返回结果
type KsAdvertiserAccountInfoSearchRes struct {
	comModel.ListRes
	List []*KsAdvertiserAccountInfoListRes `json:"list"`
}

type UpLoadVideoReq struct {
	//advertiser_id
	AdvertiserId int64 `p:"advertiserId" v:"required#主键ID不能为空" dc:"广告主ID"`
	// FileUrl
	FileUrl string `p:"fileUrl" v:"required#文件路径不能为空" dc:"文件路径"`
}

type UpLoadVideoRes struct {
	PhotoId    string `json:"photo_id"`
	PicId      string `json:"pic_id"`
	ImageToken string `json:"image_token"`
	Signature  string `json:"signature"`
}

// KsAdvertiserAccountInfoAddReq 添加操作请求参数
type KsAdvertiserAccountInfoAddReq struct {
	AccountId             int64   `p:"accountId" v:"required#主键ID不能为空" dc:"广告主ID"`
	AgentAccountId        int64   `p:"agentAccountId"  dc:"代理商账户id"`
	AuthorizeKsAccount    int64   `p:"authorizeKsAccount"  dc:"授权快手账号(代理商)"`
	UserId                int64   `p:"userId"  dc:"快手账户ID"`
	AccountName           string  `p:"accountName" v:"required#快手账户名称不能为空" dc:"快手账户名称"`
	ResponsiblePerson     string  `p:"responsiblePerson"  dc:"销售责任人"`
	UcType                string  `p:"ucType"  dc:"账户类型"`
	PaymentType           string  `p:"paymentType"  dc:"付款类型"`
	Balance               float64 `p:"balance"  dc:"现金余额"`
	CreditBalance         float64 `p:"creditBalance"  dc:"信用账户余额"`
	ExtendedBalance       float64 `p:"extendedBalance"  dc:"预留账户余额"`
	Rebate                int64   `p:"rebate"  dc:"后返余额"`
	PreRebate             int64   `p:"preRebate"  dc:"前返余额"`
	ContractRebate        int64   `p:"contractRebate"  dc:"框返余额"`
	TotalBalance          int64   `p:"totalBalance"  dc:"总余额"`
	LoLimit               int64   `p:"loLimit"  dc:"账户最低余额"`
	SingleOut             int64   `p:"singleOut"  dc:"单次转账金额"`
	AutoOut               int     `p:"autoOut"  dc:"自动转账状态"`
	BalanceWarn           int     `p:"balanceWarn"  dc:"余额不足提醒"`
	ProductName           string  `p:"productName" v:"required#产品名称不能为空" dc:"产品名称"`
	FirstCostDay          string  `p:"firstCostDay"  dc:"首日消耗日期"`
	Industry              string  `p:"industry"  dc:"一级行业"`
	SecondIndustry        string  `p:"secondIndustry"  dc:"二级行业"`
	Recharged             int     `p:"recharged"  dc:"是否充值"`
	CorporationName       string  `p:"corporationName" v:"required#企业名称不能为空" dc:"企业名称"`
	ReviewStatus          int     `p:"reviewStatus" v:"required#审核状态 1-审核中; 2-审核通过; 3-审核拒绝; 0-待提交不能为空" dc:"审核状态 1-审核中; 2-审核通过; 3-审核拒绝; 0-待提交"`
	FrozenStatus          int     `p:"frozenStatus" v:"required#冻结状态不能为空" dc:"冻结状态"`
	TransferAccountStatus int     `p:"transferAccountStatus" v:"required#转账状态不能为空" dc:"转账状态"`
	ChildReviewStatusInfo string  `p:"childReviewStatusInfo" v:"required#审核状态信息（嵌套结构体）不能为空" dc:"审核状态信息（嵌套结构体）"`
	CopyAccount           int     `p:"copyAccount"  dc:"是否为复制账户"`
	ReviewDetail          string  `p:"reviewDetail"  dc:"审核详情（数组嵌套结构体）"`
	DirectRebate          int64   `p:"directRebate"  dc:"激励余额"`
	OptimizerOwner        string  `p:"optimizerOwner"  dc:"优化师责任人"`
	AccountAutoManage     int     `p:"accountAutoManage"  dc:"账户智投开关"`
	DayBudget             int64   `p:"dayBudget"  dc:"单日预算 单位：厘"`
	Remark                string  `p:"remark"  dc:"备注"`
	AuthSource            string  `p:"authSource"  dc:"授权来源（如代理商账户授权 1 广告主授权 2）"`
	AuthStatus            int     `p:"authStatus" v:"required#授权状态（0:待授权, 1:已授权）不能为空" dc:"授权状态（0:待授权, 1:已授权）"`
	DeliveryStatus        int     `p:"deliveryStatus" v:"required#投放状态（0:停用, 1:启用）不能为空" dc:"投放状态（0:停用, 1:启用）"`
	Owner                 int     `p:"owner"  dc:"归属人员"`
	DeliveryType          int     `p:"deliveryType"  dc:"投放方式（0:默认, 1:优先效果）暂时先没用到"`
	EffectFirst           int     `p:"effectFirst"  dc:"优先效果策略（1:开启, 其他:未开启）暂时先没用到"`
	CreateTime            int64   `p:"createTime"  dc:"创建时间"`
}

// KsAdvertiserAccountInfoEditReq 修改操作请求参数
type KsAdvertiserAccountInfoEditReq struct {
	AccountId      int64  `p:"accountId" v:"required#主键ID不能为空" dc:"广告主ID"`
	Owner          int    `p:"owner"  dc:"归属人员"`
	DeliveryStatus int    `p:"deliveryStatus" v:"required#投放状态（0:停用, 1:启用）不能为空" dc:"投放状态（0:停用, 1:启用）"`
	Remark         string `p:"remark"  dc:"备注"`
}

type GetCorporationListRes struct {
	comModel.ListRes
	List []*CorporationInfo `json:"list" dc:"账户主体列表"`
}

type CorporationInfo struct {
	CorporationName string `json:"corporationName" dc:"账户主体"`
}

type GetAccountAutoInfoRes struct {
	*ksApi.AccountSimpleQueryResp863Snake
}

type GetAccountIncExploreRes struct {
	List []ksApi.GwIncExploreDetailView `json:"list" dc:"增量探索配置列表"`
}

// AddAccountIncExploreReq 新增增量探索配置请求参数
type AddAccountIncExploreReq struct {
	AdvertiserId   int64                         `p:"advertiser_id" v:"required#账户ID不能为空" dc:"账户ID"`
	IncExploreInfo []ksApi.GwIncExploreDetailDto `p:"inc_explore_info" dc:"增量探索配置列表"`
}

// AddAccountIncExploreRes 新增增量探索配置响应
type AddAccountIncExploreRes struct {
	List []ksApi.GwIncExploreDetailDto `json:"list" dc:"增量探索配置列表"`
}

// UpdateAccountIncExploreReq 编辑增量探索配置请求参数
type UpdateAccountIncExploreReq struct {
	AdvertiserId int64 `p:"advertiser_id" v:"required#账户ID不能为空" dc:"账户ID"`
	ksApi.GwIncExploreDetailDto
}

// UpdateAccountIncExploreRes 编辑增量探索配置响应
type UpdateAccountIncExploreRes struct {
	List []ksApi.GwIncExploreDetailDto `json:"list" dc:"增量探索配置列表"`
}

// DeleteAccountIncExploreReq 删除增量探索配置请求参数
type DeleteAccountIncExploreReq struct {
	AdvertiserId       int64 `p:"advertiser_id" v:"required#账户ID不能为空" dc:"账户ID"`
	OcpxActionType     int64 `p:"ocpx_action_type" v:"required#转化目标类型不能为空" json:"ocpxActionType" dc:"转化目标类型"`
	DeepConversionType int64 `p:"deep_conversion_type" v:"required#深度转化目标类型不能为空" json:"deepConversionType" dc:"深度转化目标类型"`
}

// DeleteAccountIncExploreRes 删除增量探索配置响应
type DeleteAccountIncExploreRes struct {
	List []ksApi.GwIncExploreDetailView `json:"list" dc:"增量探索配置列表"`
}

// PauseAccountIncExploreReq 暂停增量探索配置请求参数
type PauseAccountIncExploreReq struct {
	AdvertiserId int64 `p:"advertiser_id" v:"required#账户ID不能为空" dc:"账户ID"`
	ksApi.GwIncExploreDetailPauseDto
}

// PauseAccountIncExploreRes 暂停增量探索配置响应
type PauseAccountIncExploreRes struct {
	List []ksApi.GwIncExploreDetailView `json:"list" dc:"增量探索配置列表"`
}

// RebootAccountIncExploreReq 重启增量探索配置请求参数
type RebootAccountIncExploreReq struct {
	AdvertiserId int64 `p:"advertiser_id" v:"required#账户ID不能为空" dc:"账户ID"`
	ksApi.GwIncExploreDetailRebootDto
}

// RebootAccountIncExploreRes 重启增量探索配置响应
type RebootAccountIncExploreRes struct {
	List []ksApi.GwIncExploreDetailView `json:"list" dc:"增量探索配置列表"`
}

type GetAccountBalanceRes struct {
	List []*GetAccountBalanceInfo `json:"list" dc:"账户余额列表"`
}

type GetAccountBalanceInfo struct {
	AdvertiserId   int64   `json:"advertiserId" dc:"账户ID"`
	AccountBalance float64 `json:"accountBalance" dc:"账户余额"`
}

type KsAdvertiserAccountInfoUpdateReq struct {
	AccountId         int64  `p:"accountId" dc:"广告主ID"`
	AccountAutoManage *int   `p:"accountAutoManage"  dc:"账户智投开关 1：开启 0：关闭"`
	DayBudget         *int64 `p:"dayBudget"  dc:"单日预算 单位：厘"`
}

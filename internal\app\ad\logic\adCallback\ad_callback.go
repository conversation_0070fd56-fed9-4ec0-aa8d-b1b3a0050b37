// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2024-11-13 10:42:39
// 生成路径: internal/app/ad/logic/ad_app_config.go
// 生成人：cq
// desc:广告应用配置表
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
	jsoniter "github.com/json-iterator/go"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	commonConsts "github.com/tiger1103/gfast/v3/internal/app/common/consts"
	commonService "github.com/tiger1103/gfast/v3/internal/app/common/service"
	oceanModel "github.com/tiger1103/gfast/v3/internal/app/oceanengine/model"
	oceanService "github.com/tiger1103/gfast/v3/internal/app/oceanengine/service"
	"github.com/tiger1103/gfast/v3/library/advertiser"
	ksApi "github.com/tiger1103/gfast/v3/library/advertiser/ks/api"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/api"
	toutiaoModel "github.com/tiger1103/gfast/v3/library/advertiser/toutiao/model"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"github.com/tiger1103/gfast/v3/library/liberr"
	"strconv"
	"strings"
	"time"
)

func init() {
	service.RegisterAdCallback(New())
}

func New() service.IAdCallback {
	return &sAdCallback{}
}

type sAdCallback struct{}

// KsAdCallBack 快手经营者数据授权回调
func (s *sAdCallback) KsAdCallBack(ctx context.Context, req *model.KsAdCallBackReq) (res *model.KsAdCallBackRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		res = new(model.KsAdCallBackRes)
		g.Log().Infof(ctx, "KsAdCallBack 快手授权回调, state: %s, code: %s, appId: %s", req.State, req.AuthCode, req.AppId)
		if req.AppId == 0 {
			ksAdService := new(ksApi.AdKSService)
			g.Cfg().MustGet(context.Background(), "advertiser.ks").Scan(&ksAdService)
			req.AppId = ksAdService.AppId
		}
		appConfig, _ := service.AdAppConfig().GetByAppId(ctx, strconv.FormatInt(req.AppId, 10))
		if appConfig == nil {
			err = errors.New("应用不存在")
			liberr.ErrIsNil(ctx, err)
		}
		// 获取token
		oauth2Response, err1 := ksApi.GetKSApiClient().GetTokenService.SetReq(ksApi.GetTokenReq{
			AppId:    req.AppId,
			Secret:   appConfig.Secret,
			AuthCode: req.AuthCode,
		}).Do()
		liberr.ErrIsNil(ctx, err1, fmt.Sprintf("获取access_token失败: %v", err1))
		g.Log().Infof(ctx, "快手oauth2授权成功：%+v", *oauth2Response.Data)
		var accessToken = oauth2Response.Data.AccessToken
		var expiresIn = oauth2Response.Data.AccessTokenExpiresIn - 60
		var refreshToken = oauth2Response.Data.RefreshToken
		var refreshTokenExpiresIn = oauth2Response.Data.RefreshTokenExpiresIn - 60
		g.Log().Infof(ctx, "快手oauth2 accessToken: %s, expiresIn: %v, refreshToken: %s, refreshTokenExpiresIn: %v", accessToken, expiresIn, refreshToken, refreshTokenExpiresIn)
		// 给渠道获取消耗的appId

		commonService.GetGoRedis().Set(ctx, ksApi.GetReTokenKey(oauth2Response.Data.AdvertiserId), fmt.Sprintf(`"%s"`, refreshToken), time.Duration(refreshTokenExpiresIn)*time.Second)
		commonService.GetGoRedis().Set(ctx, ksApi.GetTokenKey(oauth2Response.Data.AdvertiserId), fmt.Sprintf(`"%s"`, accessToken), time.Duration(expiresIn)*time.Second)
		res.AdvertiserId = oauth2Response.Data.AdvertiserId
		var strList []string
		strList = append(strList, strconv.FormatInt(oauth2Response.Data.AdvertiserId, 10))
		key := fmt.Sprintf("%s%v", ksApi.KsAdvertiserList, req.AppId)
		commonService.GetGoRedis().Del(ctx, key)
		err = commonService.GetGoRedis().SAdd(ctx, key, strList).Err()
		if err != nil {
			g.Log().Error(ctx, fmt.Sprintf("------------- KsAdCallBack  commonService.GetGoRedis().SAdd key:%v  err：%v --------------------", key, err))
		}
		_, err = service.KsAdAccountInfo().GetKsAdAccountList(ctx, accessToken, res.AdvertiserId, req.AppId)
		service.KsAdInfo().Add(ctx, &model.KsAdInfoAddReq{
			AdvertiserId:    res.AdvertiserId,
			AppId:           int(req.AppId),
			AdAccountName:   " ",
			AccountMainName: " ",
			Status:          "authorized",
			AuthTime:        gtime.New(),
			AuthUrl:         "",
		})
		_, err = service.KsAccountSeries().GetKsAdAccountSeriesList(ctx, accessToken, res.AdvertiserId, true)

	})
	return
}

// KsAdCallBack2 快手代理商授权回调
func (s *sAdCallback) KsAdCallBack2(ctx context.Context, req *model.KsAdCallBackReq) (res *model.KsAdCallBackRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		res = new(model.KsAdCallBackRes)
		g.Log().Infof(ctx, "KsAdCallBack2 快手授权回调, state: %s, code: %s, appId: %s", req.State, req.AuthCode, req.AppId)
		//if req.AppId == 0 {
		//	err = errors.New("应用不存在")
		//	liberr.ErrIsNil(ctx, err)
		//}
		appConfig, _ := service.AdAppConfig().GetKsAppConfigByUserId(ctx, req.State)
		if appConfig == nil {
			err = errors.New("应用不存在")
			liberr.ErrIsNil(ctx, err)
		}
		req.AppId = gconv.Int64(appConfig.AppId)
		// 获取token
		oauth2Response, err1 := ksApi.GetKSApiClient().GetTokenService.SetReq(ksApi.GetTokenReq{
			AppId:    gconv.Int64(appConfig.AppId),
			Secret:   appConfig.Secret,
			AuthCode: req.AuthCode,
		}).Do()
		liberr.ErrIsNil(ctx, err1, fmt.Sprintf("获取access_token失败: %v", err1))
		g.Log().Infof(ctx, "KsAdCallBack2 快手oauth2授权成功：%+v", *oauth2Response.Data)
		var accessToken = oauth2Response.Data.AccessToken
		var expiresIn = oauth2Response.Data.AccessTokenExpiresIn - 60
		var refreshToken = oauth2Response.Data.RefreshToken
		var refreshTokenExpiresIn = oauth2Response.Data.RefreshTokenExpiresIn - 60

		// 给渠道获取消耗的appId
		if oauth2Response.Data.AgentId > 0 {
			commonService.GetGoRedis().Set(ctx, ksApi.GetAgentReTokenKey(oauth2Response.Data.AgentId, oauth2Response.Data.UserId), fmt.Sprintf(`"%s"`, refreshToken), time.Duration(refreshTokenExpiresIn)*time.Second)
			commonService.GetGoRedis().Set(ctx, ksApi.GetAgentTokenKey(oauth2Response.Data.AgentId, oauth2Response.Data.UserId), fmt.Sprintf(`"%s"`, accessToken), time.Duration(expiresIn)*time.Second)
			res.AgentId = gconv.Int64(oauth2Response.Data.AgentId)
			res.UserId = oauth2Response.Data.UserId
			err = service.KsAdvertiserAgentInfo().Add(ctx, &model.KsAdvertiserAgentInfoAddReq{
				AgentAccountId:     gconv.Int64(oauth2Response.Data.AgentId),
				AuthorizeKsAccount: oauth2Response.Data.UserId,
				Owner:              gconv.Int(req.State),
				IsShare:            0,
				AppId:              int(req.AppId),
			})
			// 拉取当前代理商账户下的所有账户信息

			if err != nil {
				g.Log().Errorf(ctx, "KsAdCallBack2 KsAdvertiserAgentInfo err:%+v", err)
			}
			libUtils.SafeGo(func() {
				innerCtx, cancel := context.WithCancel(context.Background())
				defer cancel()
				err = service.KsAdvertiserAccountInfo().PullByAgentId(innerCtx, gconv.Int64(oauth2Response.Data.AgentId), gconv.Int64(oauth2Response.Data.UserId))
				if err != nil {
					g.Log().Errorf(innerCtx, "KsAdCallBack2 PullByAgentId err:%+v", err)
				}
			})

		} else {
			commonService.GetGoRedis().Set(ctx, ksApi.GetAdvertiserReTokenKey(oauth2Response.Data.AdvertiserId, oauth2Response.Data.UserId), fmt.Sprintf(`"%s"`, refreshToken), time.Duration(refreshTokenExpiresIn)*time.Second)
			commonService.GetGoRedis().Set(ctx, ksApi.GetAdvertiserTokenKey(oauth2Response.Data.AdvertiserId, oauth2Response.Data.UserId), fmt.Sprintf(`"%s"`, accessToken), time.Duration(expiresIn)*time.Second)
			res.AdvertiserId = gconv.Int64(oauth2Response.Data.AdvertiserId)
			// 根据授权的账号获取广告主账户信息
			AdvertiserInfo, err := ksApi.GetKSApiClient().GetAdvertiserInfoService.AccessToken(accessToken).SetReq(ksApi.GetAdvertiserInfoReq{
				AdvertiserId: gconv.Int(res.AdvertiserId),
			}).Do()
			liberr.ErrIsNil(ctx, err, fmt.Sprintf("获取AdvertiserInfo失败: %v", err))
			// 拉取广告计划
			err = service.KsAdvertiserCampaign().PullCampaignByAdId(ctx, accessToken, res.AdvertiserId)
			// 拉取广告组
			err = service.KsAdvertiserUnit().PullUnitByAdId(ctx, accessToken, res.AdvertiserId)
			accountInfo := &model.KsAdvertiserAccountInfoAddReq{
				AccountId:          gconv.Int64(oauth2Response.Data.AdvertiserId),
				AccountName:        AdvertiserInfo.Data.UserName,
				AgentAccountId:     oauth2Response.Data.AgentId,
				Remark:             "",
				AuthorizeKsAccount: oauth2Response.Data.UserId,
				AuthSource:         "2",
				UserId:             AdvertiserInfo.Data.UserID,
				AuthStatus:         1,
				DeliveryStatus:     1,
				Owner:              gconv.Int(req.State),
				DeliveryType:       AdvertiserInfo.Data.DeliveryType,
				EffectFirst:        AdvertiserInfo.Data.EffectFirst,
				CorporationName:    AdvertiserInfo.Data.CorporationName,
				ProductName:        AdvertiserInfo.Data.ProductName,
				Industry:           AdvertiserInfo.Data.PrimaryIndustryName,
				SecondIndustry:     AdvertiserInfo.Data.IndustryName,
			}
			// 拉取账户日预算查询
			budget, err := ksApi.GetKSApiClient().QueryAccountBudgetService.AccessToken(accessToken).SetReq(ksApi.QueryAccountBudgetReq{
				AdvertiserId: gconv.Int64(res.AdvertiserId),
			}).Do()
			if err != nil {
				g.Log().Errorf(ctx, "ks_ad_callback_log_error拉取账户日预算查询失败 %v", err.Error())
			} else {
				if budget != nil && budget.Data != nil {
					dData := *budget.Data
					if dData.DayBudgetSchedule != nil && len(dData.DayBudgetSchedule) > 0 {
						accountInfo.DayBudget = dData.DayBudgetSchedule[0]
					} else {
						accountInfo.DayBudget = dData.DayBudget
					}
				}
			}
			// 查询账户智投配置信息 QueryAccountAutoInfoService
			aoutoInfo, _ := ksApi.GetKSApiClient().QueryAccountAutoInfoService.AccessToken(accessToken).SetReq(ksApi.QueryAccountAutoInfoReq{
				AdvertiserId: gconv.Int64(res.AdvertiserId),
			}).Do()
			if err != nil {
				g.Log().Errorf(ctx, "ks_ad_callback_log_error拉取账户日预算查询失败 %v", err.Error())
			} else {
				if aoutoInfo != nil && aoutoInfo.Data != nil {
					accountInfo.AccountAutoManage = aoutoInfo.Data.AccountAutoManage
				}
			}
			// 获取广告余额
			fund, _ := ksApi.GetKSApiClient().QueryAdvertiserFundService.AccessToken(accessToken).SetReq(ksApi.QueryAdvertiserFundReq{
				AdvertiserID: gconv.Int64(res.AdvertiserId),
			}).Do()
			if err != nil {
				g.Log().Errorf(ctx, "ks_ad_callback_log_error拉取账户日预算查询失败 %v", err.Error())
			} else {
				if fund != nil && fund.Data != nil {
					accountInfo.Balance = fund.Data.Balance
				}
			}

			err = service.KsAdvertiserAccountInfo().Add(ctx, accountInfo)
			if err != nil {
				g.Log().Errorf(ctx, "ks_ad_callback_log_error拉取账户信息保存失败 %v", err.Error())
			}
		}

	})
	return
}

// DzAdCallBack 点众回调
func (s *sAdCallback) DzAdCallBack(ctx context.Context, req *model.DzAdCallBackReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		g.Log().Infof(ctx, "--------------- DzAdCallBack 点众回调, type: %d, data: %s ", req.Type, string(req.Data))
		libUtils.SafeGo(func() {
			innerCtx, cancel := context.WithCancel(context.Background())
			defer cancel()
			switch req.Type {
			case 1:
				var reg model.DzRegisterCallBack
				if err := json.Unmarshal(req.Data, &reg); err != nil {
					g.Log().Error(innerCtx, fmt.Sprintf("DzRegisterCallBack 添加ecpm序列化失败失败: %v \n err:%v", string(req.Data), err))
					return
				}
				// 根据id进行查询
				userInfo, _ := service.DzAdUserInfo().GetByUserId(innerCtx, reg.UserId)
				if userInfo != nil && len(userInfo.UserId) > 0 {
					err = service.DzAdUserInfo().Edit(innerCtx, &model.DzAdUserInfoEditReq{
						UserId:       reg.UserId,
						Time:         gconv.Int64(reg.Time),
						ChannelId:    reg.ChannelId,
						AppId:        reg.AppId,
						PromotionId:  reg.PromotionId,
						OpenId:       reg.OpenId,
						AdId:         reg.AdId,
						BookId:       reg.BookId,
						ProjectId:    reg.ProjectId,
						ClickId:      reg.ClickId,
						UnionId:      reg.UnionId,
						RegisterTime: gconv.Int64(reg.RegisterTime),
						DyeTime:      gconv.Int64(reg.DyeTime),
					})
				} else {
					err = service.DzAdUserInfo().Add(innerCtx, &model.DzAdUserInfoAddReq{
						UserId:       reg.UserId,
						Time:         gconv.Int64(reg.Time),
						ChannelId:    reg.ChannelId,
						AppId:        reg.AppId,
						PromotionId:  reg.PromotionId,
						OpenId:       reg.OpenId,
						AdId:         reg.AdId,
						BookId:       reg.BookId,
						ProjectId:    reg.ProjectId,
						ClickId:      reg.ClickId,
						UnionId:      reg.UnionId,
						RegisterTime: gconv.Int64(reg.RegisterTime),
						DyeTime:      gconv.Int64(reg.DyeTime),
					})
				}
				// 记录日志
				if err != nil {
					g.Log().Error(innerCtx, fmt.Sprintf("DzAdCallBack DzAdUserInfogit  添加用户信息失败: %v \n err:%v", string(req.Data), err))
				}
				return
			case 2:
				var ecpm model.DzAdECPMCallBackRes
				if err := json.Unmarshal(req.Data, &ecpm); err != nil {
					// 记录日志
					g.Log().Error(innerCtx, fmt.Sprintf("DzAdECPMCallBackRes 添加ecpm序列化失败失败: %v \n err:%v", string(req.Data), err))
					return
				}
				err = service.DzAdEcpm().Add(innerCtx, &model.DzAdEcpmAddReq{
					Time:        gconv.Int64(ecpm.Time),
					UserId:      ecpm.UserId,
					ChannelId:   ecpm.ChannelId,
					AppId:       ecpm.AppId,
					PromotionId: ecpm.PromotionId,
					OpenId:      ecpm.OpenId,
					EcpmId:      ecpm.EcpmId,
					EcpmCost:    ecpm.EcpmCost,
					AdType:      ecpm.AdType,
					EventTime:   gconv.Int64(ecpm.EventTime),
					DyeTime:     gconv.Int64(ecpm.DyeTime),
					CreateDate:  libUtils.GetDateBySec(gconv.Int64(ecpm.EventTime)),
				})
				// 记录日志
				if err != nil {
					g.Log().Error(innerCtx, fmt.Sprintf("DzAdCallBack DzAdEcpm 添加ecpm失败: %v \n err:%v", string(req.Data), err))
				}
				return
			default:
				err = errors.New("unsupported type")
				return
			}
		})
	})
	return
}

func (s *sAdCallback) OceanEngineCallback(ctx context.Context, req *model.OceanEngineCallbackReq) (res *model.OceanEngineCallbackRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		g.Log().Infof(ctx, "接收到巨量oauth2回调, state: %s, code: %s, appId: %v", req.State, req.AuthCode, req.AppId)
		appConfig, _ := service.AdAppConfig().GetByAppId(ctx, strconv.FormatInt(*req.AppId, 10))
		if appConfig == nil {
			err = errors.New("应用不存在")
			liberr.ErrIsNil(ctx, err)
		}
		oauth2Response, err1 := advertiser.GetToutiaoApiClient().Oauth2AccessTokenApiService.
			Oauth2AccessTokenRequest(toutiaoModel.Oauth2AccessTokenRequest{
				AppId:    req.AppId,
				AuthCode: req.AuthCode,
				Secret:   appConfig.Secret,
			}).Do()
		liberr.ErrIsNil(ctx, err1, fmt.Sprintf("获取access_token失败: %v", err1))
		g.Log().Infof(ctx, "巨量oauth2授权成功：%+v", *oauth2Response.Data)
		var accessToken = *oauth2Response.Data.AccessToken
		var expiresIn = *oauth2Response.Data.ExpiresIn
		var refreshToken = *oauth2Response.Data.RefreshToken
		var refreshTokenExpiresIn = *oauth2Response.Data.RefreshTokenExpiresIn
		g.Log().Infof(ctx, "巨量oauth2 accessToken: %s, expiresIn: %v, refreshToken: %s, refreshTokenExpiresIn: %v", accessToken, expiresIn, refreshToken, refreshTokenExpiresIn)
		// 给渠道获取消耗的appId
		// 判断req.State 是否是以_ 结尾
		laohoutai := false
		if strings.HasSuffix(req.State, "_2") {
			// 去除 _
			req.State = strings.TrimSuffix(req.State, "_2")
			laohoutai = true
		}

		if *req.AppId == g.Cfg().MustGet(ctx, "advertiser.toutiao.appId").Int64() {
			commonService.GetGoRedis().Set(ctx, fmt.Sprintf("%s%v", advertiser.OceanengineRefreshToken, req.State), fmt.Sprintf(`"%s"`, refreshToken), time.Duration(refreshTokenExpiresIn)*time.Second)
			commonService.GetGoRedis().Set(ctx, fmt.Sprintf("%s%v", advertiser.OceanengineAccessToken, req.State), fmt.Sprintf(`"%s"`, accessToken), time.Duration(expiresIn)*time.Second)
			return
		}
		// 1. 获取已授权账户
		advertiserInfoResponse, err2 := advertiser.GetToutiaoApiClient().Oauth2AdvertiserGetApiService.AccessToken(accessToken).Do()
		liberr.ErrIsNil(ctx, err2, fmt.Sprintf("获取已授权账户失败: %v", err2))

		// 2. 获取授权User信息
		userInfoResponse, err3 := advertiser.GetToutiaoApiClient().UserInfoV2ApiService.AccessToken(accessToken).Do()
		liberr.ErrIsNil(ctx, err3, fmt.Sprintf("获取授权User信息失败: %v", err3))
		userName := strconv.FormatInt(*userInfoResponse.Data.Id, 10)
		userEmail := *userInfoResponse.Data.Email
		displayName := *userInfoResponse.Data.DisplayName

		// 3. 更新应用的授权用户数
		existAdMajordomo, _ := oceanService.AdMajordomoAdvertiserAccount().GetByUserName(ctx, userName, appConfig.AppId)
		if existAdMajordomo == nil {
			err5 := service.AdAppConfig().Edit(ctx, &model.AdAppConfigEditReq{
				Id:       appConfig.Id,
				AuthNums: appConfig.AuthNums + 1,
			})
			liberr.ErrIsNil(ctx, err5, "更新应用授权用户数失败")
		}

		// 4. 保存授权的账户
		majordomoAdvertiserList := make([]*oceanModel.AdMajordomoAdvertiserAccountAddReq, 0)
		majordomoIds := make([]string, 0)
		for _, listInner := range advertiserInfoResponse.Data.List {
			majordomoId := strconv.FormatInt(*listInner.AdvertiserId, 10)
			majordomoIds = append(majordomoIds, majordomoId)
			var accountRole = *listInner.AccountRole
			majordomoAdvertiser := &oceanModel.AdMajordomoAdvertiserAccountAddReq{
				MajordomoId:          majordomoId,
				MajordomoNick:        *listInner.AdvertiserName,
				UserId:               libUtils.ParsNum(req.State),
				MajordomoType:        accountRole,
				MajordomoUserName:    userName,
				MajordomoUserEmail:   userEmail,
				MajordomoDisplayName: displayName,
				AuthStatus:           1,
				AccessToken:          accessToken,
				RefreshToken:         refreshToken,
				AppId:                appConfig.AppId,
				Expired:              1,
			}
			// 5. 获取账户主体
			if accountRole == commonConsts.AGENT && len(listInner.CompanyList) == 0 {
				agentInfoGetRes, err6 := advertiser.GetToutiaoApiClient().AgentInfoGetV2ApiService.
					AccessToken(accessToken).
					AgentInfoGetV2Request(api.AgentInfoGetV2Request{
						AdvertiserIds: &[]int64{*listInner.AdvertiserId},
					}).Do()
				liberr.ErrIsNil(ctx, err6, "获取账户主体失败")
				if len(agentInfoGetRes.Data) > 0 {
					majordomoAdvertiser.MajordomoCompany = *agentInfoGetRes.Data[0].CompanyName
				}
			} else if accountRole == commonConsts.CUSTOMER_ADMIN || accountRole == commonConsts.CUSTOMER_OPERATOR {
				majordomoCompany, err6 := advertiser.GetToutiaoApiClient().BusinessPlatformCompanyInfoGetV3ApiService.
					AccessToken(accessToken).
					BusinessPlatformCompanyInfoGetV3Request(toutiaoModel.BusinessPlatformCompanyInfoGetV3Request{
						OrganizationId: listInner.AdvertiserId,
					}).Do()
				liberr.ErrIsNil(ctx, err6, "获取账户主体失败")
				if len(majordomoCompany.Data.CompanyInfo) > 0 {
					majordomoAdvertiser.MajordomoCompany = *majordomoCompany.Data.CompanyInfo[0].CompanyName
				}
			}
			majordomoAdvertiserList = append(majordomoAdvertiserList, majordomoAdvertiser)
		}
		_ = oceanService.AdMajordomoAdvertiserAccount().BatchAdd(ctx, majordomoAdvertiserList)
		res = &model.OceanEngineCallbackRes{
			MajordomoIds: majordomoIds,
		}
		if laohoutai {
			res.EvnType = 2
		}
	})
	return
}

// OceanEngineXTCallback 星图回调
func (s *sAdCallback) OceanEngineXTCallback(ctx context.Context, req *model.OceanEngineXTCallbackReq) (res *model.OceanEngineCallbackRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		g.Log().Infof(ctx, "OceanEngineXTCallback接收到巨量oauth2回调,   code: %s, appId: %v", req.AuthCode, &req.AppId)
		appConfig, _ := service.AdAppConfig().GetByAppId(ctx, strconv.FormatInt(req.AppId, 10))
		if appConfig == nil {
			err = errors.New("应用不存在")
			liberr.ErrIsNil(ctx, err)
		}
		oauth2Response, err1 := advertiser.GetToutiaoApiClient().Oauth2AccessTokenApiService.
			Oauth2AccessTokenRequest(toutiaoModel.Oauth2AccessTokenRequest{
				AppId:    &req.AppId,
				AuthCode: req.AuthCode,
				Secret:   appConfig.Secret,
			}).Do()
		liberr.ErrIsNil(ctx, err1, fmt.Sprintf("获取access_token失败: %v", err1))
		g.Log().Infof(ctx, "巨量oauth2授权成功：%+v", *oauth2Response.Data)
		var accessToken = *oauth2Response.Data.AccessToken
		var expiresIn = *oauth2Response.Data.ExpiresIn
		var refreshToken = *oauth2Response.Data.RefreshToken
		var refreshTokenExpiresIn = *oauth2Response.Data.RefreshTokenExpiresIn
		g.Log().Infof(ctx, "巨量oauth2 accessToken: %s, expiresIn: %v, refreshToken: %s, refreshTokenExpiresIn: %v", accessToken, expiresIn, refreshToken, refreshTokenExpiresIn)

		// 1. 获取已授权账户
		advertiserInfoResponse, err2 := advertiser.GetToutiaoApiClient().Oauth2AdvertiserGetApiService.AccessToken(accessToken).Do()
		liberr.ErrIsNil(ctx, err2, fmt.Sprintf("获取已授权账户失败: %v", err2))

		// 4. 保存授权的账户
		addList := make([]*model.AdXtAccountAddReq, 0)
		majordomoIds := make([]string, 0)
		for _, listInner := range advertiserInfoResponse.Data.List {
			majordomoId := strconv.FormatInt(*listInner.AdvertiserId, 10)
			majordomoIds = append(majordomoIds, majordomoId)
			advertiserAccount := &model.AdXtAccountAddReq{
				AdvertiserId:          majordomoId,
				AdvertiserNick:        *listInner.AdvertiserName,
				RoleName:              *listInner.AccountRole,
				AuthTime:              gtime.Now(),
				AppId:                 gconv.Int64(appConfig.AppId),
				AccessToken:           accessToken,
				RefreshToken:          refreshToken,
				Status:                "authorized",
				ExpiresIn:             gtime.Now().Add(time.Duration(expiresIn) * time.Second), // 过期时间
				RefreshTokenExpiresIn: gtime.Now().Add(time.Duration(refreshTokenExpiresIn) * time.Second),
			}
			addList = append(addList, advertiserAccount)
		}
		_ = service.AdXtAccount().AddBatch(ctx, addList)
		res = &model.OceanEngineCallbackRes{
			MajordomoIds: majordomoIds,
		}
	})
	return
}

func (s *sAdCallback) OceanEngineSubscribeValid(ctx context.Context, req *model.OceanEngineSubscribeValidReq) (res *model.OceanEngineSubscribeValidRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		g.Log().Infof(ctx, "巨量广告OceanCallbackValid req: %+v", req)
		challenge := ghttp.RequestFromCtx(ctx).GetQuery("challenge").Int64()
		g.Log().Infof(ctx, "巨量广告OceanCallbackValid: %v", challenge)
		res = &model.OceanEngineSubscribeValidRes{
			BaseResp: &model.BaseResp{
				StatusCode:    200,
				StatusMessage: "ok",
			},
			Challenge: challenge,
		}
	})
	return
}

func (s *sAdCallback) OceanEngineSubscribe(ctx context.Context) (res *model.OceanEngineSubscribeValidRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		request := ghttp.RequestFromCtx(ctx)
		body := request.GetBody()
		g.Log().Infof(ctx, "巨量广告回调body: %s", string(body))
		secretKey := g.Cfg().MustGet(ctx, "advertiser.toutiao.secretKey").String()
		util := libUtils.AuthTokenUtil{SecretKey: secretKey}
		isValidToken := util.IsValidToken(ctx, body, []byte(request.GetHeader("X-Open-Signature")))
		res = &model.OceanEngineSubscribeValidRes{
			BaseResp: &model.BaseResp{
				StatusCode:    400,
				StatusMessage: "fail",
			},
		}
		// 验签失败
		if !isValidToken {
			err = fmt.Errorf("invalid sinature")
			g.Log().Errorf(ctx, "OceanEngineSubscribe 验签名失败, err: %v", err)
			return
		}
		var subscribeData *model.OceanEngineSubscribeData
		err1 := json.Unmarshal(body, &subscribeData)
		if err1 != nil {
			g.Log().Errorf(ctx, "Unmarshal subscribeData err: %v", err1)
			return
		}
		var statDate = gtime.NewFromTimeStamp(subscribeData.PublishTime).Format("Y-m-d")
		switch subscribeData.ServiceLabel {
		case commonConsts.ReportAdvertiser:
			for _, advertiserId := range subscribeData.AdvertiserIds {
				_ = oceanService.AdAdvertiserAccountMetricsData().AdAdvertiserAccountReportSubTask(ctx, gconv.String(advertiserId), statDate, true)
			}
			break
		case commonConsts.ReportProject:
			var reportProjectData *model.ReportProjectData
			err2 := jsoniter.UnmarshalFromString(subscribeData.Data, &reportProjectData)
			if err2 != nil {
				g.Log().Errorf(ctx, "Unmarshal reportProjectData err: %v", err2)
				break
			}
			projectIds := make([]string, 0)
			for _, projectId := range reportProjectData.ProjectIds {
				projectIds = append(projectIds, gconv.String(projectId))
			}
			_ = oceanService.AdProjectMetricsData().UpdateMetricsByPIds(ctx, projectIds, statDate, true)
			break
		case commonConsts.ReportPromotion:
			var reportPromotionData *model.ReportPromotionData
			err2 := jsoniter.UnmarshalFromString(subscribeData.Data, &reportPromotionData)
			if err2 != nil {
				g.Log().Errorf(ctx, "Unmarshal reportPromotionData err: %v", err2)
				break
			}
			promotionIds := make([]string, 0)
			for _, promotionId := range reportPromotionData.PromotionIds {
				promotionIds = append(promotionIds, gconv.String(promotionId))
			}
			_ = oceanService.AdPromotionMetricsData().UpdateMetricsByPIds(ctx, promotionIds, statDate, true)
			break
		case commonConsts.StatusProject:
			var projectData *model.ProjectData
			err2 := jsoniter.UnmarshalFromString(subscribeData.Data, &projectData)
			if err2 != nil {
				g.Log().Errorf(ctx, "Unmarshal projectData err: %v", err2)
				break
			}
			var projectContent *model.ProjectContent
			err2 = jsoniter.UnmarshalFromString(projectData.Content, &projectContent)
			if err2 != nil {
				g.Log().Errorf(ctx, "Unmarshal projectContent err: %v", err2)
				break
			}
			_, _ = oceanService.AdProject().SyncAdProject(ctx, "", []string{gconv.String(projectData.UserId)}, projectContent.ProjectIds)
			break
		case commonConsts.StatusPromotion:
			var promotionData *model.PromotionData
			err2 := jsoniter.UnmarshalFromString(subscribeData.Data, &promotionData)
			if err2 != nil {
				g.Log().Errorf(ctx, "Unmarshal promotionData err: %v", err2)
				break
			}
			var promotionContent *model.PromotionContent
			err2 = jsoniter.UnmarshalFromString(promotionData.Content, &promotionContent)
			if err2 != nil {
				g.Log().Errorf(ctx, "Unmarshal promotionContent err: %v", err2)
				break
			}
			_, _ = oceanService.AdPromotion().SyncAdPromotion(ctx, "", []string{gconv.String(promotionData.UserId)}, promotionContent.PromotionIds)
			break
		default:
			break
		}
		// 验签通过, 数据处理流程
		res = &model.OceanEngineSubscribeValidRes{
			BaseResp: &model.BaseResp{
				StatusCode:    200,
				StatusMessage: "ok",
			},
		}
	})
	return
}

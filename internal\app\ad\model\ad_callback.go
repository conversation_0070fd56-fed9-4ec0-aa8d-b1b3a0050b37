package model

import "encoding/json"

type KsAdCallBackReq struct {
	AppId    int64  `json:"app_id"`
	State    string `json:"state"`
	AuthCode string `json:"auth_code"`
}

type DzAdCallBackReq struct {
	Type int             `json:"type"`
	Data json.RawMessage `json:"data"`
}
type DzCallBackData struct {
	DzRegisterCallBack
	DzAdECPMCallBackRes
}

type DzRegisterCallBack struct {
	Time         string `json:"time"`
	UserId       string `json:"user_id"`
	ChannelId    string `json:"channel_id"`
	AppId        string `json:"app_id"`
	PromotionId  string `json:"promotion_id"`
	OpenId       string `json:"open_id"`
	AdId         string `json:"ad_id"`
	BookId       string `json:"book_id"`
	ProjectId    string `json:"project_id"`
	ClickId      string `json:"click_id"`
	UnionId      string `json:"union_id"`
	RegisterTime string `json:"register_time"`
	DyeTime      string `json:"dye_time"`
}

type DzAdECPMCallBackRes struct {
	Time        string `json:"time"`
	UserId      string `json:"user_id"`
	ChannelId   string `json:"channel_id"`
	AppId       string `json:"app_id"`
	PromotionId string `json:"promotion_id"`
	OpenId      string `json:"open_id"`
	EcpmId      string `json:"ecpm_id"`
	EcpmCost    string `json:"ecpm_cost"`
	AdType      string `json:"ad_type"`
	EventTime   string `json:"event_time"`
	DyeTime     string `json:"dye_time"`
}

type OceanEngineCallbackReq struct {
	State    string `json:"state"`
	AuthCode string `json:"auth_code"`
	AppId    *int64 `json:"app_id"`
}

type KsAdCallBackRes struct {
	AdvertiserId int64  `json:"advertiser id"`
	AppId        string `json:"app_id"`
	AgentId      int64  `json:"agent_id"` // 代理商id
	UserId       int64  `json:"user_id"`  // 授权快手号
}

type OceanEngineXTCallbackReq struct {
	AuthCode string `json:"auth_code"`
	AppId    int64  `json:"app_id"`
}

type OceanEngineCallbackRes struct {
	MajordomoIds []string `json:"majordomo_ids" dc:"授权的管家账户ID"`
	EvnType      int32    `json:"evn_type" dc:"环境类型"` // 1 投放 2.老后台
}

type OceanEngineSubscribeValidReq struct {
	Challenge int64  `json:"challenge" dc:"随机数"`
	Event     string `json:"event" dc:"事件值，verify_webhook为Challenge验证事件"`
}

type OceanEngineSubscribeValidRes struct {
	BaseResp  *BaseResp `json:"BaseResp"`
	Challenge int64     `json:"challenge"`
}

type BaseResp struct {
	StatusCode    int    `json:"StatusCode" dc:"执行状态码，200表示成功，4xx 业务异常不重试，5xx 系统异常重试"`
	StatusMessage string `json:"StatusMessage" dc:"执行结果"`
}

type OceanEngineSubscribeData struct {
	MessageId       string  `json:"message_id" dc:"消息ID，唯一标识"`
	SubscribeTaskId int64   `json:"subscribe_task_id" dc:"订阅任务ID"`
	AdvertiserIds   []int64 `json:"advertiser_ids" dc:"消息对应的广告主账号，report.advertiser.beforeday /report.advertiser.activeprogram 时返回所有已产出的广告主ID组，其余服务类型一次仅返回一个广告主ID"`
	AccountRelation string  `json:"account_relation" dc:"账号对应关系，包含所有advertiser_ids的授权关系"`
	ServiceLabel    string  `json:"service_label" dc:"订阅服务类型"`
	PublishTime     int64   `json:"publish_time" dc:"消息实际产生时间，毫秒时间戳"`
	Timestamp       int64   `json:"timestamp" dc:"毫秒时间戳，本条消息实际推送时间，获取推送数据列表时无该参数数据"`
	Nonce           int64   `json:"nonce" dc:"随机数，和timestamp组合防重放，获取推送数据列表时无该参数数据"`
	Data            string  `json:"data" dc:"推送数据信息，订阅服务类型不同，返回结构不同"`
}

type ReportProjectData struct {
	AppId      int64   `json:"app_id" dc:"应用ID"`
	CoreUserId int64   `json:"core_user_id" dc:"用户ID"`
	ProjectIds []int64 `json:"project_ids" dc:"项目ID"`
}

type ReportPromotionData struct {
	AppId        int64   `json:"app_id" dc:"应用ID"`
	CoreUserId   int64   `json:"core_user_id" dc:"用户ID"`
	PromotionIds []int64 `json:"promotion_ids" dc:"广告计划ID"`
}

type ProjectData struct {
	Event   string `json:"event"`
	UserId  int64  `json:"user_id"`
	Content string `json:"content"`
}

type ProjectContent struct {
	ProjectIds   []int64  `json:"project_ids" dc:"状态发生变更的项目ids"`
	StatusFirst  string   `json:"status_first" dc:"项目一级状态"`
	StatusSecond []string `json:"status_second" dc:"项目二级状态"`
}

type PromotionData struct {
	Event   string `json:"event"`
	UserId  int64  `json:"user_id"`
	Content string `json:"content"`
}

type PromotionContent struct {
	PromotionIds []int64  `json:"promotion_ids" dc:"状态发生变更的广告ids"`
	StatusFirst  string   `json:"status_first" dc:"广告一级状态"`
	StatusSecond []string `json:"status_second" dc:"广告二级状态"`
}

// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-08-19 10:36:49
// 生成路径: internal/app/ad/logic/ks_advertiser_common_asset_category.go
// 生成人：cq
// desc:快手通用资产-标题分类
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	systemModel "github.com/tiger1103/gfast/v3/internal/app/system/model"
	sysService "github.com/tiger1103/gfast/v3/internal/app/system/service"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterKsAdvertiserCommonAssetCategory(New())
}

func New() service.IKsAdvertiserCommonAssetCategory {
	return &sKsAdvertiserCommonAssetCategory{}
}

type sKsAdvertiserCommonAssetCategory struct{}

func (s *sKsAdvertiserCommonAssetCategory) List(ctx context.Context, req *model.KsAdvertiserCommonAssetCategorySearchReq) (listRes *model.KsAdvertiserCommonAssetCategorySearchRes, err error) {
	listRes = new(model.KsAdvertiserCommonAssetCategorySearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		userInfo := sysService.Context().GetLoginUser(ctx)
		userIds, admin, _ := sysService.SysUser().GetContainUser(ctx, &systemModel.ContextUser{
			LoginUserRes: &systemModel.LoginUserRes{
				Id:     userInfo.Id,
				DeptId: userInfo.DeptId,
			},
		})
		m := dao.KsAdvertiserCommonAssetCategory.Ctx(ctx).WithAll()
		if !admin && len(userIds) > 0 {
			m = m.WhereIn(dao.KsAdvertiserCommonAssetCategory.Columns().UserId, userIds)
		}
		m = m.WhereLike(dao.KsAdvertiserCommonAssetCategory.Columns().Category, "%"+req.Category+"%")
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "id desc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.KsAdvertiserCommonAssetCategoryListRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.KsAdvertiserCommonAssetCategoryListRes, len(res))
		for k, v := range res {
			listRes.List[k] = &model.KsAdvertiserCommonAssetCategoryListRes{
				Id:        v.Id,
				Category:  v.Category,
				UserId:    v.UserId,
				CreatedAt: v.CreatedAt,
			}
		}
	})
	return
}

func (s *sKsAdvertiserCommonAssetCategory) GetById(ctx context.Context, id int) (res *model.KsAdvertiserCommonAssetCategoryInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.KsAdvertiserCommonAssetCategory.Ctx(ctx).WithAll().Where(dao.KsAdvertiserCommonAssetCategory.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sKsAdvertiserCommonAssetCategory) Add(ctx context.Context, req *model.KsAdvertiserCommonAssetCategoryAddReq) (lastInsertId int64, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		userId := sysService.Context().GetUserId(ctx)
		lastInsertId, err = dao.KsAdvertiserCommonAssetCategory.Ctx(ctx).InsertAndGetId(do.KsAdvertiserCommonAssetCategory{
			Category: req.Category,
			UserId:   userId,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sKsAdvertiserCommonAssetCategory) Edit(ctx context.Context, req *model.KsAdvertiserCommonAssetCategoryEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.KsAdvertiserCommonAssetCategory.Ctx(ctx).WherePri(req.Id).Update(do.KsAdvertiserCommonAssetCategory{
			Category: req.Category,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sKsAdvertiserCommonAssetCategory) Delete(ctx context.Context, ids []int) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.KsAdvertiserCommonAssetCategory.Ctx(ctx).Delete(dao.KsAdvertiserCommonAssetCategory.Columns().Id+" in (?)", ids)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}
